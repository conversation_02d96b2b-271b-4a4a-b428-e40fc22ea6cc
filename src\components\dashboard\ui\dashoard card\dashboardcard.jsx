"use client";

import React from "react";

const DashboardCard = ({
  title = "Tickets Solved",
  value = "356",
  percentage = "+11.01%",
  isPositive = true,
  backgroundColor = "#F7F5FF",
  className = "",
  ...props
}) => {
  return (
    <div
      className={`flex flex-col justify-between p-4 ${className}`}
      style={{
        width: "210.417px",
        height: "116.667px",
        flexShrink: 0,
        borderRadius: "16.667px",
        background: backgroundColor,
        ...props.style,
      }}
      {...props}
    >
      {/* Title */}
      <div
        className="text-gray-600"
        style={{
          fontFamily: "Poppins, sans-serif",
          fontWeight: 400,
          fontSize: "12px",
          lineHeight: "16px",
          color: "#6B7280",
        }}
      >
        {title}
      </div>

      {/* Value and Percentage Container */}
      <div className="flex items-end justify-between">
        {/* Main Value */}
        <div
          className="text-gray-900"
          style={{
            fontFamily: "Poppins, sans-serif",
            fontWeight: 700,
            fontSize: "24px",
            lineHeight: "32px",
            color: "#1F2937",
          }}
        >
          {value}
        </div>

        {/* Percentage with Arrow */}
        <div className="flex items-center gap-1">
          <span
            className={isPositive ? "text-green-600" : "text-red-600"}
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 500,
              fontSize: "10px",
              lineHeight: "14px",
              color: isPositive ? "#059669" : "#DC2626",
            }}
          >
            {percentage}
          </span>

          {/* Arrow Icon */}
          <svg
            width="8"
            height="8"
            viewBox="0 0 8 8"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={
              isPositive ? "text-green-600" : "text-red-600 rotate-180"
            }
          >
            <path
              d="M4 1L7 4L4 7M1 4H7"
              stroke="currentColor"
              strokeWidth="1"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};

export default DashboardCard;
