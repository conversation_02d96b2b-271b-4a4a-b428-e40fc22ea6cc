"use client";
import Navbar from "../navbar";

import React from "react";

const Hero = () => {
  return (
    <section
      className="bg-cover bg-center bg-no-repeat min-h-screen w-full flex flex-col justify-center items-center relative
                 p-4 sm:p-8 md:p-12 lg:p-16 xl:p-20"
      style={{
        backgroundImage: "url('/background.png')",
      }}
    >
      <div className="absolute top-4 left-0 right-0 z-10">
        <Navbar />
      </div>

      {/* Content Container - Centered */}
      <div className="w-full flex flex-col items-center justify-center text-center z-10 max-w-4xl mx-auto px-4">
        {/* Main Title */}
        <h1
          className="mb-6 lg:mb-8 text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[52px]"
          style={{
            color: "#FFFEFB",
            fontFamily: "Pop<PERSON>s",
            fontWeight: 700,
            lineHeight: "1.35",
          }}
        >
          Présentation de l'organisation
        </h1>

        {/* Description */}
        <p
          className="mb-8 lg:mb-10 text-sm sm:text-base md:text-lg lg:text-xl xl:text-[24px] text-center max-w-3xl"
          style={{
            color: "#1F2421",
            fontFamily: "Poppins",
            fontWeight: 600,
            lineHeight: "1.54",
          }}
        >
          Association caritative algérienne dédiée à l'aide humanitaire et
          éducative, reflet des valeurs de solidarité du peuple.
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 lg:gap-[30px]">
          <button
            className="text-white font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full sm:w-auto px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-3"
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: "clamp(8px, 1vw, 13.418px)",
              borderRadius: "clamp(12px, 1.2vw, 16.102px)",
              background: "#4B935E",
              padding:
                "clamp(10px, 1vw, 13.418px) clamp(20px, 2.5vw, 33.545px)",
            }}
          >
            Contribuer
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5 12H19M19 12L12 5M19 12L12 19"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <button
            className="hover:bg-[#4B935E] hover:text-white transition-colors duration-200 w-full sm:w-auto text-sm sm:text-base md:text-lg lg:text-xl xl:text-[26.836px] px-4 py-2 sm:px-6 sm:py-3"
            style={{
              borderRadius: "clamp(12px, 1.2vw, 16.102px)",
              border: "clamp(1.5px, 0.15vw, 2.013px) solid #4B935E",
              background: "#FFFEFB",
              color: "#4B935E",
              fontFamily: "Poppins",
              fontWeight: 500,
              lineHeight: "normal",
            }}
          >
            en savoir plus
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
