"use client";
import Navbar from "../navbar";

import React from "react";

const Hero = () => {
  return (
    <section
      className="bg-cover bg-center bg-no-repeat min-h-screen w-full flex flex-col justify-end items-start relative
                 p-4 sm:p-8 md:p-12 lg:p-16 xl:p-20
                 gap-4 sm:gap-6 md:gap-8 lg:gap-10 xl:gap-[35.734px]
                 2xl:w-[1440px] 2xl:h-[827px] 2xl:justify-end 2xl:items-start"
      style={{
        backgroundImage: "url('/background.png')",
        paddingTop: "clamp(60px, 8vw, 116.134px)",
        paddingRight: "clamp(20px, 24vw, 341.255px)",
        paddingBottom: "clamp(30px, 4vw, 57.174px)",
        paddingLeft: "clamp(20px, 4vw, 57.174px)",
      }}
    >
      <div className="absolute top-4 left-0 right-0 z-10">
        <Navbar />
      </div>

      {/* Content Container */}
      <div className="w-full max-w-none sm:max-w-lg md:max-w-2xl lg:max-w-4xl xl:max-w-[816px] z-10">
        {/* Main Title */}
        <h1
          className="mb-6 lg:mb-8 text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-[52px]"
          style={{
            maxWidth: "816px",
            width: "100%",
            color: "#FFFEFB",
            fontFamily: "Poppins",
            fontWeight: 700,
            lineHeight: "1.35",
          }}
        >
          Présentation de l'organisation
        </h1>

        {/* Description */}
        <p
          className="mb-8 lg:mb-10 text-sm sm:text-base md:text-lg lg:text-xl xl:text-[24px] text-center"
          style={{
            maxWidth: "771px",
            width: "100%",
            color: "#1F2421",
            fontFamily: "Poppins",
            fontWeight: 600,
            lineHeight: "1.54",
          }}
        >
          Association caritative algérienne dédiée à l'aide humanitaire et
          éducative, reflet des valeurs de solidarité du peuple.
        </p>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row items-center justify-start gap-4 sm:gap-6 lg:gap-[30px] w-full max-w-[538px]">
          <button
            className="text-white font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full sm:w-auto px-4 py-2 sm:px-6 sm:py-3 lg:px-8 lg:py-3"
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              gap: "clamp(8px, 1vw, 13.418px)",
              borderRadius: "clamp(12px, 1.2vw, 16.102px)",
              background: "#4B935E",
              padding:
                "clamp(10px, 1vw, 13.418px) clamp(20px, 2.5vw, 33.545px)",
            }}
          >
            Contribuer
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M5 12H19M19 12L12 5M19 12L12 19"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <button
            className="hover:bg-[#4B935E] hover:text-white transition-colors duration-200 w-full sm:w-auto text-sm sm:text-base md:text-lg lg:text-xl xl:text-[26.836px] px-4 py-2 sm:px-6 sm:py-3"
            style={{
              borderRadius: "clamp(12px, 1.2vw, 16.102px)",
              border: "clamp(1.5px, 0.15vw, 2.013px) solid #4B935E",
              background: "#FFFEFB",
              color: "#4B935E",
              fontFamily: "Poppins",
              fontWeight: 500,
              lineHeight: "normal",
            }}
          >
            en savoir plus
          </button>
        </div>
      </div>
    </section>
  );
};

export default Hero;
