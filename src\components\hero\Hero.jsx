"use client";
import Navbar from "../navbar";

import React from "react";

const Hero = () => {
  return (
    <section className="w-full mx-auto px-4 lg:px-[97px] pt-8 pb-16 lg:pt-12 lg:pb-24">
      <Navbar />
      <div className="max-w-[1440px] mx-auto">
        {/* Content */}
        <div className="max-w-[650px]">
          {/* Main Title */}
          <h1
            className="text-[#4B935E] mb-6 lg:mb-8"
            style={{
              width: "816px",
              height: "70px",
              fontFamily: "Poppins, sans-serif",
              fontWeight: 700,
              fontSize: "52px",
              lineHeight: "70px",
              letterSpacing: "0%",
              opacity: 1,
            }}
          >
            Présentation de l'organisation
          </h1>

          {/* Description */}
          <p
            className="text-[#1F2421] mb-8 lg:mb-10"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 500,
              fontSize: "clamp(18px, 2vw, 24px)",
              lineHeight: "clamp(28px, 3vw, 37px)",
              letterSpacing: "0%",
            }}
          >
            L'organisation de l'Homme Algérien est une organisation caritative à
            but non lucratif spécialisée dans le travail humanitaire et
            éducatif. Ce nom a été choisi pour refléter la véritable nature du
            peuple algérien, qui tend la main à ses frères dans le besoin, et
            pour donner une image positive de l'Algérie et de ses habitants
            auprès des autres peuples.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-start gap-[30px] w-full max-w-[538px]">
            <button
              className="bg-[#4B935E] text-white font-medium hover:bg-[#3d7a4e] transition-colors duration-200 flex items-center w-full sm:w-auto"
              style={{
                width: "252.09039306640625px",
                height: "66.8361587524414px",
                borderRadius: "16.1px",
                paddingTop: "13.42px",
                paddingRight: "33.55px",
                paddingBottom: "13.42px",
                paddingLeft: "33.55px",
                gap: "13.42px",
                opacity: 1,
                fontFamily: "Poppins, sans-serif",
                fontWeight: 500,
                fontSize: "16px",
              }}
            >
              Contribuer
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19M19 12L12 5M19 12L12 19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            <button
              className="border-[#4B935E] text-[#4B935E] font-medium hover:bg-[#4B935E] hover:text-white transition-colors duration-200 w-full sm:w-auto"
              style={{
                width: "255.09039306640625px",
                height: "66.8361587524414px",
                borderRadius: "16.1px",
                paddingTop: "13.42px",
                paddingRight: "33.55px",
                paddingBottom: "13.42px",
                paddingLeft: "33.55px",
                gap: "13.42px",
                opacity: 1,
                borderWidth: "2.01px",
                fontFamily: "Poppins, sans-serif",
                fontWeight: 500,
                fontSize: "16px",
              }}
            >
              en savoir plus
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
