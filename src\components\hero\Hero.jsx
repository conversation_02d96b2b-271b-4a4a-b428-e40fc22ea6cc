"use client";
import Navbar from "../navbar";

import React from "react";

const Hero = () => {
  return (
    <section
      className="w-full mx-auto px-4 lg:px-[97px] pt-8 pb-16 lg:pt-12 lg:pb-24 bg-cover bg-center bg-no-repeat"
      style={{
        backgroundImage: "url('/background.png')",
      }}
    >
      <Navbar />
      <div className="max-w-[1440px] mx-auto">
        {/* Content */}
        <div className="max-w-[816px] mx-auto text-center">
          {/* Main Title */}
          <h1
            className="mb-6 lg:mb-8"
            style={{
              width: "816px",
              color: "#FFFEFB",
              fontFamily: "Poppins",
              fontSize: "52px",
              fontWeight: 700,
              lineHeight: "70px",
            }}
          >
            Présentation de l'organisation
          </h1>

          {/* Description */}
          <p
            className="mb-8 lg:mb-10"
            style={{
              width: "771px",
              color: "#1F2421",
              textAlign: "center",
              fontFamily: "Poppins",
              fontSize: "24px",
              fontWeight: 600,
              lineHeight: "37px",
            }}
          >
            Association caritative algérienne dédiée à l'aide humanitaire et
            éducative, reflet des valeurs de solidarité du peuple.
          </p>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-[30px] w-full max-w-[538px] mx-auto">
            <button
              className="text-white font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full sm:w-auto"
              style={{
                display: "flex",
                padding: "13.418px 33.545px",
                justifyContent: "center",
                alignItems: "center",
                gap: "13.418px",
                borderRadius: "16.102px",
                background: "#4B935E",
              }}
            >
              Contribuer
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M5 12H19M19 12L12 5M19 12L12 19"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>

            <button
              className="hover:bg-[#4B935E] hover:text-white transition-colors duration-200 w-full sm:w-auto"
              style={{
                color: "#4B935E",
                fontFamily: "Poppins",
                fontSize: "26.836px",
                fontWeight: 500,
                lineHeight: "normal",
              }}
            >
              en savoir plus
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
