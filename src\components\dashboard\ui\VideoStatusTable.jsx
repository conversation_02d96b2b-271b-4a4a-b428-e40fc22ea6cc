"use client";

import React, { useState } from "react";

const VideoStatusTable = ({
  videos = [],
  onVideoClick,
  onActionClick,
  className = "",
  ...props
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");

  const getStatusBadge = (status) => {
    const statusConfig = {
      pending: {
        bg: "bg-yellow-100",
        text: "text-yellow-800",
        border: "border-yellow-200",
        label: "Pending",
      },
      sent: {
        bg: "bg-blue-100",
        text: "text-blue-800",
        border: "border-blue-200",
        label: "Sent",
      },
      confirmed: {
        bg: "bg-green-100",
        text: "text-green-800",
        border: "border-green-200",
        label: "Confirmed",
      },
      failed: {
        bg: "bg-red-100",
        text: "text-red-800",
        border: "border-red-200",
        label: "Failed",
      },
    };

    const config = statusConfig[status] || statusConfig.pending;

    return (
      <span
        className={`
          inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border
          ${config.bg} ${config.text} ${config.border}
        `}
        style={{
          fontFamily: "Poppins, sans-serif",
          fontWeight: 500,
        }}
      >
        {config.label}
      </span>
    );
  };

  const filteredVideos = videos.filter((video) => {
    const matchesSearch = video.orderId?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         video.fileName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "all" || video.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
    <div
      className={`
        bg-white
        border border-[#E5E7EB]
        rounded-lg
        shadow-sm
        ${className}
      `}
      style={{
        boxShadow: "0px 2px 4px -2px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.1)",
      }}
      {...props}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#E5E7EB]">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h3
            className="text-[#1F2421] text-lg font-semibold"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 600,
            }}
          >
            Video Confirmations
          </h3>
          
          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-3">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder="Search orders or files..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="
                  w-full sm:w-64 px-3 py-2 pl-10
                  border border-[#E5E7EB] rounded-lg
                  text-sm placeholder-gray-500
                  focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent
                "
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 400,
                }}
              />
              <svg
                className="absolute left-3 top-2.5 h-4 w-4 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>

            {/* Status Filter */}
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="
                px-3 py-2 border border-[#E5E7EB] rounded-lg text-sm
                focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent
              "
              style={{
                fontFamily: "Poppins, sans-serif",
                fontWeight: 400,
              }}
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="sent">Sent</option>
              <option value="confirmed">Confirmed</option>
              <option value="failed">Failed</option>
            </select>
          </div>
        </div>
      </div>

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 500,
                }}
              >
                Order ID
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 500,
                }}
              >
                Video File
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 500,
                }}
              >
                Status
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 500,
                }}
              >
                Timestamp
              </th>
              <th
                className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 500,
                }}
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredVideos.length === 0 ? (
              <tr>
                <td colSpan="5" className="px-6 py-8 text-center">
                  <div className="text-gray-500">
                    <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V4a1 1 0 011-1h2a1 1 0 011 1v3" />
                    </svg>
                    <p
                      className="text-sm"
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: 400,
                      }}
                    >
                      No videos found
                    </p>
                  </div>
                </td>
              </tr>
            ) : (
              filteredVideos.map((video, index) => (
                <tr
                  key={video.id || index}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onVideoClick && onVideoClick(video)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="text-sm font-medium text-[#1F2421]"
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: 500,
                      }}
                    >
                      {video.orderId}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="text-sm text-gray-600"
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: 400,
                      }}
                    >
                      {video.fileName}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(video.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className="text-sm text-gray-600"
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: 400,
                      }}
                    >
                      {video.timestamp}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onActionClick && onActionClick(video);
                      }}
                      className="
                        text-[#4B935E] hover:text-[#3d7a4e] 
                        font-medium text-sm transition-colors duration-200
                      "
                      style={{
                        fontFamily: "Poppins, sans-serif",
                        fontWeight: 500,
                      }}
                    >
                      View Details
                    </button>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default VideoStatusTable;
