"use client";

import React, { useState } from "react";
import Link from "next/link";

const SignupPage = () => {
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    telephone: "",
    dateNaissance: "",
    motDePasse: "",
    confirmationMotDePasse: "",
    fichier: null,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showFileOptions, setShowFileOptions] = useState(false);
  const [showDocumentOptions, setShowDocumentOptions] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState("");

  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === "file" ? files[0] : value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.motDePasse !== formData.confirmationMotDePasse) {
      alert("Les mots de passe ne correspondent pas");
      return;
    }
    console.log("Signup attempt:", formData);
    // Add your signup logic here
  };

  const handleFileUpload = () => {
    setShowFileOptions(true);
  };

  const handleDocumentTypeSelect = (type) => {
    setSelectedDocumentType(type);
    setShowFileOptions(false);
    setShowDocumentOptions(true);
  };

  const handleCameraCapture = () => {
    // Implement camera capture logic
    console.log("Camera capture for", selectedDocumentType);
    setShowDocumentOptions(false);
  };

  const handleFileSelect = () => {
    // Trigger file input
    document.getElementById("fileInput").click();
    setShowDocumentOptions(false);
  };

  return (
    <div className="min-h-screen w-full flex items-center justify-center py-8" style={{ background: "#FFFEFB" }}>
      <div className="w-full max-w-md mx-auto p-6">
        {/* Title */}
        <div className="text-center mb-8">
          <h1
            className="text-[#4B935E] mb-4"
            style={{
              fontFamily: "Poppins",
              fontWeight: 700,
              fontSize: "32px",
              lineHeight: "1.4",
            }}
          >
            Inscription
          </h1>
          <p
            className="text-gray-600"
            style={{
              fontFamily: "Poppins",
              fontWeight: 400,
              fontSize: "16px",
              lineHeight: "1.5",
            }}
          >
            Rejoignez-nous pour aider les autres
          </p>
        </div>

        {/* Signup Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Nom Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Nom *
            </label>
            <input
              type="text"
              name="nom"
              value={formData.nom}
              onChange={handleInputChange}
              placeholder="Nom"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Prenom Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Prénom *
            </label>
            <input
              type="text"
              name="prenom"
              value={formData.prenom}
              onChange={handleInputChange}
              placeholder="Prénom"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Phone Number Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Numéro de téléphone *
            </label>
            <input
              type="tel"
              name="telephone"
              value={formData.telephone}
              onChange={handleInputChange}
              placeholder="+0000000"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Date of Birth Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Date de naissance *
            </label>
            <input
              type="date"
              name="dateNaissance"
              value={formData.dateNaissance}
              onChange={handleInputChange}
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Password Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Mot de passe *
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="motDePasse"
                value={formData.motDePasse}
                onChange={handleInputChange}
                placeholder="••••••••"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                {showPassword ? "🙈" : "👁️"}
              </button>
            </div>
          </div>

          {/* Confirm Password Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Confirmation du Mot de passe *
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmationMotDePasse"
                value={formData.confirmationMotDePasse}
                onChange={handleInputChange}
                placeholder="••••••••"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                {showConfirmPassword ? "🙈" : "👁️"}
              </button>
            </div>
          </div>

          {/* File Upload Field */}
          <div>
            <label className="block text-gray-700 text-sm font-medium mb-2" style={{ fontFamily: "Poppins" }}>
              Ajouter un fichier *
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                placeholder="Aucun fichier"
                value={formData.fichier ? formData.fichier.name : ""}
                readOnly
                className="flex-1 p-4 border border-gray-200 rounded-lg bg-gray-50 text-gray-700 placeholder-gray-400"
                style={{ fontFamily: "Poppins" }}
              />
              <button
                type="button"
                onClick={handleFileUpload}
                className="bg-[#4B935E] text-white px-6 py-4 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200"
                style={{ fontFamily: "Poppins", fontSize: "14px" }}
              >
                Ajouter fichier
              </button>
            </div>
            <p className="text-gray-500 text-xs mt-2" style={{ fontFamily: "Poppins" }}>
              Veuillez ajouter une photo de carte d'identité ou passeport
            </p>
          </div>

          {/* Hidden file input */}
          <input
            type="file"
            id="fileInput"
            name="fichier"
            onChange={handleInputChange}
            accept="image/*"
            className="hidden"
          />

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-[#4B935E] text-white py-4 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200"
            style={{
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 500,
            }}
          >
            Confirmer
          </button>
        </form>

        {/* Login Link */}
        <div className="text-center mt-8">
          <p className="text-gray-600" style={{ fontFamily: "Poppins", fontSize: "14px" }}>
            Vous avez déjà un compte ?{" "}
            <Link
              href="/login"
              className="text-[#4B935E] font-medium hover:underline transition-all duration-200"
            >
              Se connecter
            </Link>
          </p>
        </div>
      </div>

      {/* File Type Selection Modal */}
      {showFileOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold mb-4 text-center" style={{ fontFamily: "Poppins" }}>
              Type de document
            </h3>
            <div className="space-y-3">
              <button
                onClick={() => handleDocumentTypeSelect("passport")}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Passeport
              </button>
              <button
                onClick={() => handleDocumentTypeSelect("id")}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Carte d'identité
              </button>
            </div>
            <button
              onClick={() => setShowFileOptions(false)}
              className="w-full mt-4 p-3 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}

      {/* Document Capture Options Modal */}
      {showDocumentOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3 className="text-lg font-semibold mb-4 text-center" style={{ fontFamily: "Poppins" }}>
              Comment ajouter votre {selectedDocumentType === "passport" ? "passeport" : "carte d'identité"} ?
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleCameraCapture}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-2"
                style={{ fontFamily: "Poppins" }}
              >
                📷 Prendre une photo
              </button>
              <button
                onClick={handleFileSelect}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-2"
                style={{ fontFamily: "Poppins" }}
              >
                📁 Choisir un fichier
              </button>
            </div>
            <button
              onClick={() => setShowDocumentOptions(false)}
              className="w-full mt-4 p-3 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default SignupPage;
