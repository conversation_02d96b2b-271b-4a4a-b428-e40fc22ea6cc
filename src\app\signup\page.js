"use client";

import React, { useState } from "react";
import Link from "next/link";

const SignupPage = () => {
  const [formData, setFormData] = useState({
    nom: "",
    prenom: "",
    telephone: "",
    dateNaissance: "",
    motDePasse: "",
    confirmationMotDePasse: "",
    fichier: null,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showFileOptions, setShowFileOptions] = useState(false);
  const [showDocumentOptions, setShowDocumentOptions] = useState(false);
  const [selectedDocumentType, setSelectedDocumentType] = useState("");

  const handleInputChange = (e) => {
    const { name, value, type, files } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "file" ? files[0] : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (formData.motDePasse !== formData.confirmationMotDePasse) {
      alert("Les mots de passe ne correspondent pas");
      return;
    }
    console.log("Signup attempt:", formData);
    // Add your signup logic here
  };

  const handleFileUpload = () => {
    setShowFileOptions(true);
  };

  const handleDocumentTypeSelect = (type) => {
    setSelectedDocumentType(type);
    setShowFileOptions(false);
    setShowDocumentOptions(true);
  };

  const handleCameraCapture = () => {
    // Implement camera capture logic
    console.log("Camera capture for", selectedDocumentType);
    setShowDocumentOptions(false);
  };

  const handleFileSelect = () => {
    // Trigger file input
    document.getElementById("fileInput").click();
    setShowDocumentOptions(false);
  };

  return (
    <div className="min-h-screen w-full flex" style={{ background: "#FFFEFB" }}>
      {/* Left Side - Hero Image/Content */}
      <div
        className="hidden lg:flex lg:w-2/5 relative bg-cover bg-center"
        style={{ backgroundImage: "url('/background.png')" }}
      >
        <div className="absolute inset-0 bg-[#4B935E] bg-opacity-70"></div>
        <div className="relative z-10 flex flex-col justify-center items-center text-white p-12">
          <div className="max-w-md text-center">
            <h1 className="text-4xl font-bold mb-6" style={{ fontFamily: "Poppins" }}>
              Rejoignez Al-Insan
            </h1>
            <p className="text-xl mb-8 opacity-90" style={{ fontFamily: "Poppins" }}>
              Ensemble, nous pouvons faire la différence dans la vie de milliers de familles
            </p>
            <div className="space-y-4">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">❤️</span>
                </div>
                <span className="text-lg" style={{ fontFamily: "Poppins" }}>15,000+ familles aidées</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🏫</span>
                </div>
                <span className="text-lg" style={{ fontFamily: "Poppins" }}>650+ élèves soutenus</span>
              </div>
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                  <span className="text-2xl">🕌</span>
                </div>
                <span className="text-lg" style={{ fontFamily: "Poppins" }}>20+ mosquées construites</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Signup Form */}
      <div className="w-full lg:w-3/5 flex items-center justify-center p-8 overflow-y-auto">
        <div className="w-full max-w-2xl py-8">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-[#4B935E] rounded-2xl flex items-center justify-center shadow-lg">
                <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 21V19C16 17.9391 15.5786 16.9217 14.8284 16.1716C14.0783 15.4214 13.0609 15 12 15H5C3.93913 15 2.92172 15.4214 2.17157 16.1716C1.42143 16.9217 1 17.9391 1 19V21" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M8.5 11C10.7091 11 12.5 9.20914 12.5 7C12.5 4.79086 10.7091 3 8.5 3C6.29086 3 4.5 4.79086 4.5 7C4.5 9.20914 6.29086 11 8.5 11Z" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M20 8V14" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M23 11H17" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <h1
              className="text-gray-900 mb-3"
              style={{
                fontFamily: "Poppins",
                fontWeight: 700,
                fontSize: "36px",
                lineHeight: "1.2",
              }}
            >
              Créer un compte
            </h1>
            <p
              className="text-gray-600 text-lg"
              style={{
                fontFamily: "Poppins",
                fontWeight: 400,
                lineHeight: "1.6",
              }}
            >
              Rejoignez notre mission humanitaire
            </p>
          </div>

          {/* Signup Form */}
          <form onSubmit={handleSubmit} className="space-y-8 max-w-xl mx-auto">
          {/* Nom Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Nom *
            </label>
            <input
              type="text"
              name="nom"
              value={formData.nom}
              onChange={handleInputChange}
              placeholder="Nom"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Prenom Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Prénom *
            </label>
            <input
              type="text"
              name="prenom"
              value={formData.prenom}
              onChange={handleInputChange}
              placeholder="Prénom"
              className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
              style={{ fontFamily: "Poppins" }}
              required
            />
          </div>

          {/* Phone Number Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Numéro de téléphone *
            </label>
            <div className="relative">
              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 flex items-center">
                <span
                  className="text-gray-500 mr-2"
                  style={{ fontFamily: "Poppins", fontSize: "16px" }}
                >
                  🇩🇿
                </span>
                <span
                  className="text-gray-500"
                  style={{ fontFamily: "Poppins", fontSize: "14px" }}
                >
                  +213
                </span>
                <div className="w-px h-6 bg-gray-300 mx-3"></div>
              </div>
              <input
                type="tel"
                name="telephone"
                value={formData.telephone}
                onChange={handleInputChange}
                placeholder="000 000 000"
                className="w-full p-4 pl-24 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M22 16.92V19.92C22.0011 20.1985 21.9441 20.4742 21.8325 20.7293C21.7209 20.9845 21.5573 21.2136 21.3521 21.4019C21.1468 21.5901 20.9046 21.7335 20.6407 21.8227C20.3769 21.9119 20.0974 21.9451 19.82 21.92C16.7428 21.5856 13.787 20.5341 11.19 18.85C8.77382 17.3147 6.72533 15.2662 5.18999 12.85C3.49997 10.2412 2.44824 7.27099 2.11999 4.18C2.095 3.90347 2.12787 3.62476 2.21649 3.36162C2.30512 3.09849 2.44756 2.85669 2.63476 2.65162C2.82196 2.44655 3.0498 2.28271 3.30379 2.17052C3.55777 2.05833 3.83233 2.00026 4.10999 2H7.10999C7.59531 1.99522 8.06579 2.16708 8.43376 2.48353C8.80173 2.79999 9.04207 3.23945 9.10999 3.72C9.23662 4.68007 9.47144 5.62273 9.80999 6.53C9.94454 6.88792 9.97366 7.27691 9.8939 7.65088C9.81415 8.02485 9.62886 8.36811 9.35999 8.64L8.08999 9.91C9.51355 12.4135 11.5865 14.4864 14.09 15.91L15.36 14.64C15.6319 14.3711 15.9751 14.1858 16.3491 14.1061C16.7231 14.0263 17.1121 14.0555 17.47 14.19C18.3773 14.5286 19.3199 14.7634 20.28 14.89C20.7658 14.9585 21.2094 15.2032 21.5265 15.5775C21.8437 15.9518 22.0122 16.4296 22 16.92Z"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Date of Birth Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Date de naissance *
            </label>
            <div className="relative">
              <input
                type="date"
                name="dateNaissance"
                value={formData.dateNaissance}
                onChange={handleInputChange}
                className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <div className="absolute right-4 top-1/2 transform -translate-y-1/2 pointer-events-none">
                <svg
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <rect
                    x="3"
                    y="4"
                    width="18"
                    height="18"
                    rx="2"
                    ry="2"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                  />
                  <line
                    x1="16"
                    y1="2"
                    x2="16"
                    y2="6"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  <line
                    x1="8"
                    y1="2"
                    x2="8"
                    y2="6"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                  <line
                    x1="3"
                    y1="10"
                    x2="21"
                    y2="10"
                    stroke="#9CA3AF"
                    strokeWidth="2"
                    strokeLinecap="round"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Password Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Mot de passe *
            </label>
            <div className="relative">
              <input
                type={showPassword ? "text" : "password"}
                name="motDePasse"
                value={formData.motDePasse}
                onChange={handleInputChange}
                placeholder="••••••••"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                {showPassword ? (
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M17.94 17.94C16.2306 19.243 14.1491 19.9649 12 20C5 20 1 12 1 12C2.24389 9.68192 4.02643 7.65663 6.17 6.06M9.9 4.24C10.5883 4.0789 11.2931 3.99836 12 4C19 4 23 12 23 12C22.393 13.1356 21.6691 14.2048 20.84 15.19M14.12 14.12C13.8454 14.4148 13.5141 14.6512 13.1462 14.8151C12.7782 14.9791 12.3809 15.0673 11.9781 15.0744C11.5753 15.0815 11.1752 15.0074 10.8016 14.8565C10.4281 14.7056 10.0887 14.4811 9.80385 14.1962C9.51897 13.9113 9.29439 13.5719 9.14351 13.1984C8.99262 12.8248 8.91853 12.4247 8.92563 12.0219C8.93274 11.6191 9.02091 11.2218 9.18488 10.8538C9.34884 10.4858 9.58525 10.1546 9.88 9.88"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M1 1L23 23"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                ) : (
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M1 12C1 12 5 4 12 4C19 4 23 12 23 12C23 12 19 20 12 20C5 20 1 12 1 12Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                )}
              </button>
            </div>
          </div>

          {/* Confirm Password Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Confirmation du Mot de passe *
            </label>
            <div className="relative">
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmationMotDePasse"
                value={formData.confirmationMotDePasse}
                onChange={handleInputChange}
                placeholder="••••••••"
                className="w-full p-4 border border-gray-200 rounded-lg bg-white text-gray-700 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                {showConfirmPassword ? "🙈" : "👁️"}
              </button>
            </div>
          </div>

          {/* File Upload Field */}
          <div>
            <label
              className="block text-gray-700 text-sm font-medium mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Ajouter un fichier *
            </label>
            <div className="flex gap-3">
              <input
                type="text"
                placeholder="Aucun fichier"
                value={formData.fichier ? formData.fichier.name : ""}
                readOnly
                className="flex-1 p-4 border border-gray-200 rounded-lg bg-gray-50 text-gray-700 placeholder-gray-400"
                style={{ fontFamily: "Poppins" }}
              />
              <button
                type="button"
                onClick={handleFileUpload}
                className="bg-[#4B935E] text-white px-6 py-4 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200"
                style={{ fontFamily: "Poppins", fontSize: "14px" }}
              >
                Ajouter fichier
              </button>
            </div>
            <p
              className="text-gray-500 text-xs mt-2"
              style={{ fontFamily: "Poppins" }}
            >
              Veuillez ajouter une photo de carte d'identité ou passeport
            </p>
          </div>

          {/* Hidden file input */}
          <input
            type="file"
            id="fileInput"
            name="fichier"
            onChange={handleInputChange}
            accept="image/*"
            className="hidden"
          />

          {/* Submit Button */}
          <button
            type="submit"
            className="w-full bg-[#4B935E] text-white py-4 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200"
            style={{
              fontFamily: "Poppins",
              fontSize: "16px",
              fontWeight: 500,
            }}
          >
            Confirmer
          </button>
        </form>

        {/* Login Link */}
        <div className="text-center mt-8">
          <p
            className="text-gray-600"
            style={{ fontFamily: "Poppins", fontSize: "14px" }}
          >
            Vous avez déjà un compte ?{" "}
            <Link
              href="/login"
              className="text-[#4B935E] font-medium hover:underline transition-all duration-200"
            >
              Se connecter
            </Link>
          </p>
        </div>
      </div>

      {/* File Type Selection Modal */}
      {showFileOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3
              className="text-lg font-semibold mb-4 text-center"
              style={{ fontFamily: "Poppins" }}
            >
              Type de document
            </h3>
            <div className="space-y-3">
              <button
                onClick={() => handleDocumentTypeSelect("passport")}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Passeport
              </button>
              <button
                onClick={() => handleDocumentTypeSelect("id")}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Carte d'identité
              </button>
            </div>
            <button
              onClick={() => setShowFileOptions(false)}
              className="w-full mt-4 p-3 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}

      {/* Document Capture Options Modal */}
      {showDocumentOptions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-sm w-full mx-4">
            <h3
              className="text-lg font-semibold mb-4 text-center"
              style={{ fontFamily: "Poppins" }}
            >
              Comment ajouter votre{" "}
              {selectedDocumentType === "passport"
                ? "passeport"
                : "carte d'identité"}{" "}
              ?
            </h3>
            <div className="space-y-3">
              <button
                onClick={handleCameraCapture}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-2"
                style={{ fontFamily: "Poppins" }}
              >
                📷 Prendre une photo
              </button>
              <button
                onClick={handleFileSelect}
                className="w-full p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 flex items-center justify-center gap-2"
                style={{ fontFamily: "Poppins" }}
              >
                📁 Choisir un fichier
              </button>
            </div>
            <button
              onClick={() => setShowDocumentOptions(false)}
              className="w-full mt-4 p-3 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              Annuler
            </button>
          </div>
        </div>
      )}
        </div>
      </div>
    </div>
  );
};

export default SignupPage;
