"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

const LoginPage = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });
  const [language, setLanguage] = useState("fr");
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Login attempt:", formData);
    // Add your login logic here
  };

  return (
    <div className="min-h-screen w-full flex" style={{ background: "#f8f9fa" }}>
      {/* Left Side - Green Gradient Card */}
      <div className="hidden lg:flex lg:w-1/2 p-8">
        <div className="w-full bg-gradient-to-br from-[#2d5a3d] via-[#4B935E] to-[#6bb77b] rounded-3xl relative overflow-hidden">
          {/* Background pattern/texture */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-20 w-40 h-40 bg-white rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-2xl"></div>
          </div>

          {/* Content */}
          <div className="relative z-10 h-full flex flex-col p-12">
            {/* Logo */}
            <div className="mb-8">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2L2 7L12 12L22 7L12 2Z"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2 17L12 22L22 17"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2 12L12 17L22 12"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {/* Go Back Link */}
            <div className="mb-12">
              <Link
                href="/"
                className="inline-flex items-center text-white text-sm font-medium hover:text-white/80 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-2"
                >
                  <path
                    d="M19 12H5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 19L5 12L12 5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Go Back
              </Link>
            </div>

            {/* Main Content - Centered */}
            <div className="flex-1 flex flex-col justify-center">
              <div className="text-center text-white">
                <h1
                  className="text-4xl font-bold mb-4"
                  style={{ fontFamily: "Poppins" }}
                >
                  Welcome back
                </h1>
                <p
                  className="text-lg opacity-90 mb-12"
                  style={{ fontFamily: "Poppins" }}
                >
                  Please enter your details
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md">
          {/* Logo for mobile */}
          <div className="lg:hidden flex items-center justify-center mb-8">
            <div className="w-16 h-16 bg-[#4B935E] rounded-2xl flex items-center justify-center shadow-lg">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2L2 7L12 12L22 7L12 2Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 17L12 22L22 17"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 12L12 17L22 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          {/* Go Back for mobile */}
          <div className="lg:hidden mb-6">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 text-sm font-medium hover:text-gray-800 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="mr-2"
              >
                <path
                  d="M19 12H5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12 19L5 12L12 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Go Back
            </Link>
          </div>

          {/* Header */}
          <div className="mb-8">
            <h1
              className="text-gray-900 text-2xl font-bold mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Welcome back
            </h1>
            <p className="text-gray-600" style={{ fontFamily: "Poppins" }}>
              Please enter your details
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label
                className="block text-gray-700 text-sm font-medium mb-2"
                style={{ fontFamily: "Poppins" }}
              >
                Email
              </label>
              <input
                type="email"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder=""
                className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
            </div>

            {/* Password Field */}
            <div>
              <label
                className="block text-gray-700 text-sm font-medium mb-2"
                style={{ fontFamily: "Poppins" }}
              >
                Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder=""
                className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="rememberMe"
                  id="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-[#4B935E] bg-white border border-gray-300 rounded focus:ring-[#4B935E] focus:ring-2 transition-all duration-200"
                />
                <label
                  htmlFor="rememberMe"
                  className="ml-2 text-gray-600 text-sm cursor-pointer"
                  style={{ fontFamily: "Poppins" }}
                >
                  Remember me
                </label>
              </div>
              <a
                href="#"
                className="text-[#4B935E] text-sm font-medium hover:text-[#3d7a4e] transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Forgot password ?
              </a>
            </div>

            {/* Sign In Button */}
            <button
              type="submit"
              className="w-full bg-[#4B935E] text-white py-3 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 mb-6"
              style={{ fontFamily: "Poppins" }}
            >
              Sign In
            </button>
          </form>



          {/* Sign Up Link */}
          <div className="text-center">
            <p
              className="text-gray-600 text-sm"
              style={{ fontFamily: "Poppins" }}
            >
              Don't have an account?{" "}
              <Link
                href="/signup"
                className="text-[#4B935E] font-medium hover:underline transition-all duration-200"
              >
                Register Here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
