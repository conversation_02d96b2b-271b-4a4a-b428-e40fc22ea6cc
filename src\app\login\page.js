"use client";

import React, { useState } from "react";
import Image from "next/image";
import Link from "next/link";

const LoginPage = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });
  const [language, setLanguage] = useState("fr");
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Login attempt:", formData);
    // Add your login logic here
  };

  return (
    <div className="min-h-screen w-full flex" style={{ background: "#f8f9fa" }}>
      {/* Left Side - Green Gradient Card */}
      <div className="hidden lg:flex lg:w-1/2 p-8">
        <div className="w-full bg-gradient-to-br from-[#2d5a3d] via-[#4B935E] to-[#6bb77b] rounded-3xl relative overflow-hidden">
          {/* Background pattern/texture */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl"></div>
            <div className="absolute bottom-20 right-20 w-40 h-40 bg-white rounded-full blur-3xl"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-2xl"></div>
          </div>

          {/* Content */}
          <div className="relative z-10 h-full flex flex-col p-12">
            {/* Logo */}
            <div className="mb-8">
              <div className="w-16 h-16 bg-white bg-opacity-20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                <svg
                  width="32"
                  height="32"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M12 2L2 7L12 12L22 7L12 2Z"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2 17L12 22L22 17"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M2 12L12 17L22 12"
                    stroke="white"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </div>
            </div>

            {/* Go Back Link */}
            <div className="mb-12">
              <Link
                href="/"
                className="inline-flex items-center text-white text-sm font-medium hover:text-white/80 transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-2"
                >
                  <path
                    d="M19 12H5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M12 19L5 12L12 5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                Go Back
              </Link>
            </div>

            {/* Main Content - Centered */}
            <div className="flex-1 flex flex-col justify-center">
              <div className="text-center text-white">
                <h1
                  className="text-4xl font-bold mb-4"
                  style={{ fontFamily: "Poppins" }}
                >
                  Welcome back
                </h1>
                <p
                  className="text-lg opacity-90 mb-12"
                  style={{ fontFamily: "Poppins" }}
                >
                  Please enter your details
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-white">
        <div className="w-full max-w-md">
          {/* Logo for mobile */}
          <div className="lg:hidden flex items-center justify-center mb-8">
            <div className="w-16 h-16 bg-[#4B935E] rounded-2xl flex items-center justify-center shadow-lg">
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M12 2L2 7L12 12L22 7L12 2Z"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 17L12 22L22 17"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M2 12L12 17L22 12"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </div>
          </div>

          {/* Go Back for mobile */}
          <div className="lg:hidden mb-6">
            <Link
              href="/"
              className="inline-flex items-center text-gray-600 text-sm font-medium hover:text-gray-800 transition-colors duration-200"
              style={{ fontFamily: "Poppins" }}
            >
              <svg
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="mr-2"
              >
                <path
                  d="M19 12H5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
                <path
                  d="M12 19L5 12L12 5"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              Go Back
            </Link>
          </div>

          {/* Header */}
          <div className="mb-8">
            <h1
              className="text-gray-900 text-2xl font-bold mb-2"
              style={{ fontFamily: "Poppins" }}
            >
              Welcome back
            </h1>
            <p className="text-gray-600" style={{ fontFamily: "Poppins" }}>
              Please enter your details
            </p>
          </div>

          {/* Login Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Email Field */}
            <div>
              <label
                className="block text-gray-700 text-sm font-medium mb-2"
                style={{ fontFamily: "Poppins" }}
              >
                Email
              </label>
              <input
                type="email"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                placeholder=""
                className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
            </div>

            {/* Password Field */}
            <div>
              <label
                className="block text-gray-700 text-sm font-medium mb-2"
                style={{ fontFamily: "Poppins" }}
              >
                Password
              </label>
              <input
                type={showPassword ? "text" : "password"}
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                placeholder=""
                className="w-full p-3 border border-gray-300 rounded-lg bg-white text-gray-700 focus:outline-none focus:ring-2 focus:ring-[#4B935E] focus:border-transparent transition-all duration-200"
                style={{ fontFamily: "Poppins" }}
                required
              />
            </div>

            {/* Remember Me & Forgot Password */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  name="rememberMe"
                  id="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  className="w-4 h-4 text-[#4B935E] bg-white border border-gray-300 rounded focus:ring-[#4B935E] focus:ring-2 transition-all duration-200"
                />
                <label
                  htmlFor="rememberMe"
                  className="ml-2 text-gray-600 text-sm cursor-pointer"
                  style={{ fontFamily: "Poppins" }}
                >
                  Remember me
                </label>
              </div>
              <a
                href="#"
                className="text-[#4B935E] text-sm font-medium hover:text-[#3d7a4e] transition-colors duration-200"
                style={{ fontFamily: "Poppins" }}
              >
                Forgot password ?
              </a>
            </div>

            {/* Sign In Button */}
            <button
              type="submit"
              className="w-full bg-[#4B935E] text-white py-3 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 mb-6"
              style={{ fontFamily: "Poppins" }}
            >
              Sign In
            </button>
          </form>

          {/* Divider */}
          <div className="relative mb-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span
                className="px-4 bg-white text-gray-500"
                style={{ fontFamily: "Poppins" }}
              >
                Or Continue With
              </span>
            </div>
          </div>

          {/* Social Login Buttons */}
          <div className="flex justify-center space-x-4 mb-8">
            {/* Apple */}
            <button className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-200">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M18.71 19.5C17.88 20.74 17 21.95 15.66 21.97C14.32 22 13.89 21.18 12.37 21.18C10.84 21.18 10.37 21.95 9.09997 22C7.78997 22.05 6.79997 20.68 5.95997 19.47C4.24997 17 2.93997 12.45 4.69997 9.39C5.56997 7.87 7.12997 6.91 8.81997 6.88C10.1 6.86 11.32 7.75 12.11 7.75C12.89 7.75 14.37 6.68 15.92 6.84C16.57 6.87 18.39 7.1 19.56 8.82C19.47 8.88 17.39 10.1 17.41 12.63C17.44 15.65 20.06 16.66 20.09 16.67C20.06 16.74 19.67 18.11 18.71 19.5ZM13 3.5C13.73 2.67 14.94 2.04 15.94 2C16.07 3.17 15.6 4.35 14.9 5.19C14.21 6.04 13.07 6.7 11.95 6.61C11.8 5.46 12.36 4.26 13 3.5Z"
                  fill="#000"
                />
              </svg>
            </button>

            {/* Google */}
            <button className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-200">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M22.56 12.25C22.56 11.47 22.49 10.72 22.36 10H12V14.26H17.92C17.66 15.63 16.88 16.79 15.71 17.57V20.34H19.28C21.36 18.42 22.56 15.6 22.56 12.25Z"
                  fill="#4285F4"
                />
                <path
                  d="M12 23C15.24 23 17.95 21.92 19.28 20.34L15.71 17.57C14.74 18.22 13.48 18.62 12 18.62C8.87 18.62 6.22 16.68 5.35 13.98H1.64V16.83C2.96 19.45 7.24 23 12 23Z"
                  fill="#34A853"
                />
                <path
                  d="M5.35 13.98C5.13 13.33 5 12.64 5 11.93C5 11.22 5.13 10.53 5.35 9.88V7.03H1.64C0.89 8.52 0.5 10.19 0.5 11.93C0.5 13.67 0.89 15.34 1.64 16.83L5.35 13.98Z"
                  fill="#FBBC05"
                />
                <path
                  d="M12 5.24C13.62 5.24 15.06 5.81 16.18 6.88L19.39 3.67C17.95 2.32 15.24 1.5 12 1.5C7.24 1.5 2.96 4.55 1.64 7.17L5.35 10.02C6.22 7.32 8.87 5.24 12 5.24Z"
                  fill="#EA4335"
                />
              </svg>
            </button>

            {/* Facebook */}
            <button className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center hover:bg-gray-200 transition-colors duration-200">
              <svg
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M24 12.073C24 5.405 18.627 0 12 0S0 5.405 0 12.073C0 18.1 4.388 23.094 10.125 24V15.564H7.078V12.073H10.125V9.405C10.125 6.348 11.917 4.688 14.658 4.688C15.97 4.688 17.344 4.922 17.344 4.922V7.875H15.83C14.34 7.875 13.875 8.8 13.875 9.75V12.073H17.203L16.671 15.564H13.875V24C19.612 23.094 24 18.1 24 12.073Z"
                  fill="#1877F2"
                />
              </svg>
            </button>
          </div>

          {/* Sign Up Link */}
          <div className="text-center">
            <p
              className="text-gray-600 text-sm"
              style={{ fontFamily: "Poppins" }}
            >
              Don't have an account?{" "}
              <Link
                href="/signup"
                className="text-[#4B935E] font-medium hover:underline transition-all duration-200"
              >
                Register Here
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
