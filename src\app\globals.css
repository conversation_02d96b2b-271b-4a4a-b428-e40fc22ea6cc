@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --main-color: #4b935e;
  --secondary-color: #fffefb;
  --border-color: #e5e7eb;
}

/* Font configuration */
.font-poppins {
  font-family: var(--font-poppins), system-ui, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb {
  background: #d4d4d4;
  border-radius: 9999px;
  transition: background-color 250ms ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a3a3a3;
}

/* Modern animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-fade-in {
  animation: fadeIn 250ms ease-out;
}

.animate-slide-in {
  animation: slideIn 250ms ease-out;
}

.animate-scale-in {
  animation: scaleIn 250ms ease-out;
}

/* Responsive utilities */
.container-responsive {
  width: 100%;
  max-width: 1440px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (min-width: 640px) {
  .container-responsive {
    padding: 0 2rem;
  }
}

@media (min-width: 1024px) {
  .container-responsive {
    padding: 0 4rem;
  }
}

@media (min-width: 1440px) {
  .container-responsive {
    padding: 0;
  }
}

/* Responsive text utilities */
.text-responsive-sm {
  font-size: clamp(0.875rem, 2vw, 1rem);
}

.text-responsive-base {
  font-size: clamp(1rem, 2.5vw, 1.125rem);
}

.text-responsive-lg {
  font-size: clamp(1.125rem, 3vw, 1.5rem);
}

.text-responsive-xl {
  font-size: clamp(1.25rem, 4vw, 2rem);
}

.text-responsive-2xl {
  font-size: clamp(1.5rem, 5vw, 3rem);
}
