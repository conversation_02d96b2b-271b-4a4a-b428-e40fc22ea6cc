@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #171717;
  --main-color: #4b935e;
  --secondary-color: #fffefb;
  --border-color: #e5e7eb;

  /* Modern design system */
  --primary-50: #f0f9f4;
  --primary-100: #dcf2e4;
  --primary-200: #bce5cd;
  --primary-300: #8dd3aa;
  --primary-400: #4b935e;
  --primary-500: #3d7a4e;
  --primary-600: #2f5f3c;
  --primary-700: #254a2f;
  --primary-800: #1e3a26;
  --primary-900: #172d1e;

  /* Neutral colors */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Semantic colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;

  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;

  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  --info-50: #eff6ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;

  /* Modern shadows */
  --shadow-xs: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-sm: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);

  /* Glassmorphism */
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-backdrop: blur(12px);

  /* Gradients */
  --gradient-primary: linear-gradient(
    135deg,
    var(--primary-400) 0%,
    var(--primary-600) 100%
  );
  --gradient-success: linear-gradient(
    135deg,
    var(--success-500) 0%,
    var(--success-600) 100%
  );
  --gradient-warning: linear-gradient(
    135deg,
    var(--warning-500) 0%,
    var(--warning-600) 100%
  );
  --gradient-error: linear-gradient(
    135deg,
    var(--error-500) 0%,
    var(--error-600) 100%
  );
  --gradient-info: linear-gradient(
    135deg,
    var(--info-500) 0%,
    var(--info-600) 100%
  );

  /* Animation durations */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;

  /* Border radius */
  --radius-xs: 4px;
  --radius-sm: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 24px;
  --radius-full: 9999px;
}

/* Font configuration */
.font-poppins {
  font-family: var(--font-poppins), system-ui, sans-serif;
}

[data-theme="dark"] {
  --background: #0a0a0a;
  --foreground: #ededed;
  --secondary-color: #111111;
  --border-color: #262626;

  /* Dark theme adjustments */
  --neutral-50: #171717;
  --neutral-100: #262626;
  --neutral-200: #404040;
  --neutral-300: #525252;
  --neutral-400: #737373;
  --neutral-500: #a3a3a3;
  --neutral-600: #d4d4d4;
  --neutral-700: #e5e5e5;
  --neutral-800: #f5f5f5;
  --neutral-900: #fafafa;

  /* Dark glassmorphism */
  --glass-bg: rgba(0, 0, 0, 0.4);
  --glass-border: rgba(255, 255, 255, 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
  transition: background-color var(--duration-normal) ease,
    color var(--duration-normal) ease;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-100);
  border-radius: var(--radius-full);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-300);
  border-radius: var(--radius-full);
  transition: background-color var(--duration-normal) ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-400);
}

/* Modern focus styles */
.focus-ring {
  outline: none;
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px rgba(255, 255, 255, 1);
}

[data-theme="dark"] .focus-ring {
  box-shadow: 0 0 0 2px var(--primary-500), 0 0 0 4px var(--neutral-900);
}

/* Glassmorphism utility */
.glass {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
}

/* Modern animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-fade-in {
  animation: fadeIn var(--duration-normal) ease-out;
}

.animate-slide-in {
  animation: slideIn var(--duration-normal) ease-out;
}

.animate-scale-in {
  animation: scaleIn var(--duration-normal) ease-out;
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Skeleton loading */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--neutral-200) 25%,
    var(--neutral-100) 50%,
    var(--neutral-200) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-md);
}

[data-theme="dark"] .skeleton {
  background: linear-gradient(
    90deg,
    var(--neutral-800) 25%,
    var(--neutral-700) 50%,
    var(--neutral-800) 75%
  );
  background-size: 200% 100%;
}
