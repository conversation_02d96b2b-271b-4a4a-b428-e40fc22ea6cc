@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --main-color: #4b935e;
  --secondary-color: #fffefb;
  --border-color: #e5e7eb;
}

/* Font configuration */
.font-poppins {
  font-family: var(--font-poppins), system-ui, sans-serif;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* Modern scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 9999px;
}

::-webkit-scrollbar-thumb {
  background: #d4d4d4;
  border-radius: 9999px;
  transition: background-color 250ms ease;
}

::-webkit-scrollbar-thumb:hover {
  background: #a3a3a3;
}

/* Modern animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-fade-in {
  animation: fadeIn 250ms ease-out;
}

.animate-slide-in {
  animation: slideIn 250ms ease-out;
}

.animate-scale-in {
  animation: scaleIn 250ms ease-out;
}
