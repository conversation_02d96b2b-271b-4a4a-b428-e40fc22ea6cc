"use client";

import React from "react";
import {
  DashboardLayout,
  StatusCard,
  AlertPanel,
  VideoStatusTable,
  ThemeProvider,
} from "../../components/dashboard/ui";

const DashboardPage = () => {
  const [isLoading, setIsLoading] = React.useState(true);

  // Simulate loading
  React.useEffect(() => {
    const timer = setTimeout(() => setIsLoading(false), 2000);
    return () => clearTimeout(timer);
  }, []);

  // Sample data with trend information
  const statusData = [
    {
      title: "Total Videos",
      value: "1,247",
      change: "+12.5%",
      changeType: "positive",
      description: "from last month",
      trend: [45, 52, 48, 61, 55, 67, 73, 69, 78, 82, 85, 89],
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <polygon
            points="23 7 16 12 23 17 23 7"
            stroke="currentColor"
            strokeWidth="2.5"
          />
          <rect
            x="1"
            y="5"
            width="15"
            height="14"
            rx="2"
            ry="2"
            stroke="currentColor"
            strokeWidth="2.5"
          />
        </svg>
      ),
    },
    {
      title: "Pending Confirmations",
      value: "23",
      change: "-8.2%",
      changeType: "positive",
      description: "from yesterday",
      trend: [35, 42, 38, 45, 41, 38, 35, 32, 28, 25, 23, 21],
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="2.5"
          />
          <polyline
            points="12,6 12,12 16,14"
            stroke="currentColor"
            strokeWidth="2.5"
          />
        </svg>
      ),
    },
    {
      title: "Failed Deliveries",
      value: "3",
      change: "+2",
      changeType: "negative",
      description: "needs attention",
      trend: [1, 2, 1, 3, 2, 4, 3, 5, 4, 3, 2, 3],
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <circle
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="2.5"
          />
          <line
            x1="15"
            y1="9"
            x2="9"
            y2="15"
            stroke="currentColor"
            strokeWidth="2.5"
          />
          <line
            x1="9"
            y1="9"
            x2="15"
            y2="15"
            stroke="currentColor"
            strokeWidth="2.5"
          />
        </svg>
      ),
    },
    {
      title: "Success Rate",
      value: "98.7%",
      change: "+0.3%",
      changeType: "positive",
      description: "this week",
      trend: [
        96.2, 97.1, 96.8, 97.5, 98.1, 97.9, 98.3, 98.5, 98.2, 98.6, 98.4, 98.7,
      ],
      icon: (
        <svg
          width="20"
          height="20"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M22 11.08V12a10 10 0 1 1-5.93-9.14"
            stroke="currentColor"
            strokeWidth="2.5"
          />
          <polyline
            points="22,4 12,14.01 9,11.01"
            stroke="currentColor"
            strokeWidth="2.5"
          />
        </svg>
      ),
    },
  ];

  const alertsData = [
    {
      id: 1,
      severity: "critical",
      title: "Video File Missing",
      message: "Order #LS-2024-0156 video file not found in storage",
      timestamp: "2 minutes ago",
    },
    {
      id: 2,
      severity: "warning",
      title: "Delivery Timeout",
      message:
        "Order #LS-2024-0142 confirmation delivery taking longer than expected",
      timestamp: "15 minutes ago",
    },
    {
      id: 3,
      severity: "info",
      title: "System Update",
      message: "Scheduled maintenance window starts in 2 hours",
      timestamp: "1 hour ago",
    },
  ];

  const videosData = [
    {
      id: 1,
      orderId: "LS-2024-0158",
      fileName: "slaughter_confirmation_158.mp4",
      status: "confirmed",
      timestamp: "2024-01-15 14:30:22",
    },
    {
      id: 2,
      orderId: "LS-2024-0157",
      fileName: "slaughter_confirmation_157.mp4",
      status: "sent",
      timestamp: "2024-01-15 14:25:18",
    },
    {
      id: 3,
      orderId: "LS-2024-0156",
      fileName: "slaughter_confirmation_156.mp4",
      status: "failed",
      timestamp: "2024-01-15 14:20:45",
    },
    {
      id: 4,
      orderId: "LS-2024-0155",
      fileName: "slaughter_confirmation_155.mp4",
      status: "pending",
      timestamp: "2024-01-15 14:15:33",
    },
    {
      id: 5,
      orderId: "LS-2024-0154",
      fileName: "slaughter_confirmation_154.mp4",
      status: "confirmed",
      timestamp: "2024-01-15 14:10:12",
    },
  ];

  const handleVideoClick = (video) => {
    console.log("Video clicked:", video);
    // Handle video detail view
  };

  const handleActionClick = (video) => {
    console.log("Action clicked for video:", video);
    // Handle video actions (retry, view details, etc.)
  };

  const handleAlertClick = (alert) => {
    console.log("Alert clicked:", alert);
    // Handle alert details
  };

  const handleAlertDismiss = (alert) => {
    console.log("Alert dismissed:", alert);
    // Handle alert dismissal
  };

  return (
    <ThemeProvider>
      <DashboardLayout currentPage="dashboard">
        <div className="space-y-6">
          {/* Page Header */}
          <div>
            <h1
              className="text-[#1F2421] text-2xl font-bold"
              style={{
                fontFamily: "Poppins, sans-serif",
                fontWeight: 700,
              }}
            >
              Livestock Processing Dashboard
            </h1>
            <p
              className="text-[#6B7280] mt-1"
              style={{
                fontFamily: "Poppins, sans-serif",
                fontWeight: 400,
              }}
            >
              Monitor video confirmations and system status in real-time
            </p>
          </div>

          {/* Status Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {statusData.map((status, index) => (
              <StatusCard
                key={index}
                title={status.title}
                value={status.value}
                change={status.change}
                changeType={status.changeType}
                description={status.description}
                icon={status.icon}
                trend={status.trend}
                isLoading={isLoading}
              />
            ))}
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Video Status Table */}
            <div className="lg:col-span-2">
              <VideoStatusTable
                videos={videosData}
                onVideoClick={handleVideoClick}
                onActionClick={handleActionClick}
              />
            </div>

            {/* Alerts Panel */}
            <div className="lg:col-span-1">
              <AlertPanel
                alerts={alertsData}
                onAlertClick={handleAlertClick}
                onDismiss={handleAlertDismiss}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ThemeProvider>
  );
};

export default DashboardPage;
