"use client";

import React from "react";
import { Card } from "../ui";

const CommentAider = () => {
  const cardsData = [
    {
      id: 1,
      heading: "Projet de sacrifices",
      description:
        "Offrez un sacrifice pour nourrir les familles dans le besoin durant l'Aïd",
      image: "/placeholder-sheepjpg.jpg",
      imageAlt: "Sheep for sacrifice project",
      buttonText: "Contribuer",
      isComingSoon: false,
    },
    {
      id: 2,
      heading: "Projet éducatif",
      description: "Soutenez l'éducation des enfants défavorisés en Algérie",
      image: "/laceholder-education.jpg",
      imageAlt: "Education project",
      buttonText: "Contribuer",
      isComingSoon: true,
    },
    {
      id: 3,
      heading: "Aide humanitaire",
      description:
        "Participez à nos missions d'aide humanitaire pour les familles en détresse",
      image: "/placeholder-humanitarian.png",
      imageAlt: "Humanitarian aid project",
      buttonText: "Contribuer",
      isComingSoon: true,
    },
  ];

  const handleCardClick = (cardId) => {
    console.log(`Card ${cardId} clicked`);
    // Add your navigation or action logic here
  };

  return (
    <section className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[98px] py-12 sm:py-16 lg:py-20">
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2
            className="text-[#4B935E] mx-auto text-2xl sm:text-3xl md:text-4xl lg:text-[45px]"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 700,
              lineHeight: "1.4",
              letterSpacing: "0%",
            }}
          >
            Comment Aider
          </h2>
        </div>

        {/* Cards Container */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-[45px] max-w-6xl mx-auto">
          {cardsData.map((card) => (
            <Card
              key={card.id}
              heading={card.heading}
              description={card.description}
              image={card.image}
              imageAlt={card.imageAlt}
              buttonText={card.buttonText}
              isComingSoon={card.isComingSoon}
              onButtonClick={() => handleCardClick(card.id)}
              className="flex-shrink-0"
            />
          ))}
        </div>

        {/* Navigation Dots */}
        <div className="flex justify-center items-center gap-3 mt-12">
          {/* Previous Button */}
          <button
            className="w-10 h-10 rounded-full bg-gray-200 text-gray-400 flex items-center justify-center hover:bg-gray-300 transition-colors duration-200"
            aria-label="Previous slide"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M15 18L9 12L15 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          {/* Dots */}
          <div className="flex items-center gap-2 mx-4">
            {cardsData.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                  index === 2 ? "bg-gray-800" : "bg-gray-300"
                }`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>

          {/* Next Button */}
          <button
            className="w-10 h-10 rounded-full bg-gray-800 text-white flex items-center justify-center hover:bg-gray-700 transition-colors duration-200"
            aria-label="Next slide"
          >
            <svg
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M9 18L15 12L9 6"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>
        </div>
      </div>
    </section>
  );
};

export default CommentAider;
