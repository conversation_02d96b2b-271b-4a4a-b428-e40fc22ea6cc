"use client";

import React from "react";

const NotreMission = () => {
  const statsData = [
    {
      number: "200+",
      description: "Projets d'eau potable",
    },
    {
      number: "50+",
      description: "Communautés aidées",
    },
    {
      number: "15,000+",
      description: "Familles soutenues",
    },
    {
      number: "1000+",
      description: "Paniers alimentaires distribués",
    },
    {
      number: "650+",
      description: "Élèves soutenus",
    },
    {
      number: "20+",
      description: "Mosquées construites",
    },
  ];

  return (
    <section className="w-full mx-auto px-4 lg:px-[98px] py-12 lg:py-16">
      <div className="max-w-[1440px] mx-auto">
        {/* Section Title */}
        <div className="text-center mb-8 lg:mb-12">
          <h2
            className="text-[#4B935E] mx-auto"
            style={{
              width: "565px",
              height: "70px",
              fontFamily: "Poppins, sans-serif",
              fontWeight: 700,
              fontSize: "45px",
              lineHeight: "70px",
              letterSpacing: "0%",
              opacity: 1,
            }}
          >
            Notre mission & impact
          </h2>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 max-w-[900px] mx-auto">
          {statsData.map((stat, index) => (
            <div key={index} className="text-center">
              <div
                className="text-[#1F2421] mb-1"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 700,
                  fontSize: "clamp(28px, 3.5vw, 42px)",
                  lineHeight: "1.1",
                }}
              >
                {stat.number}
              </div>
              <p
                className="text-[#6B7280]"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                }}
              >
                {stat.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NotreMission;
