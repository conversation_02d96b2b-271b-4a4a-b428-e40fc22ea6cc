"use client";

import React from "react";

const NotreMission = () => {
  const statsData = [
    {
      number: "200+",
      description: "Projets d'eau potable",
    },
    {
      number: "50+",
      description: "Communautés aidées",
    },
    {
      number: "15,000+",
      description: "Familles soutenues",
    },
    {
      number: "1000+",
      description: "Paniers alimentaires distribués",
    },
    {
      number: "650+",
      description: "Élèves soutenus",
    },
    {
      number: "20+",
      description: "Mosquées construites",
    },
  ];

  return (
    <section className="w-full px-4 sm:px-6 md:px-8 lg:px-12 xl:px-[98px] py-12 sm:py-16 lg:py-20">
      <div className="max-w-7xl mx-auto">
        {/* Section Title */}
        <div className="text-center mb-8 sm:mb-12 lg:mb-16">
          <h2
            className="text-[#4B935E] mx-auto text-2xl sm:text-3xl md:text-4xl lg:text-[45px]"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 700,
              lineHeight: "1.4",
              letterSpacing: "0%",
            }}
          >
            Notre mission & impact
          </h2>
        </div>

        {/* Statistics Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12 max-w-5xl mx-auto">
          {statsData.map((stat, index) => (
            <div key={index} className="text-center">
              <div
                className="text-[#1F2421] mb-1"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 700,
                  fontSize: "clamp(28px, 3.5vw, 42px)",
                  lineHeight: "1.1",
                }}
              >
                {stat.number}
              </div>
              <p
                className="text-[#6B7280]"
                style={{
                  fontFamily: "Poppins, sans-serif",
                  fontWeight: 400,
                  fontSize: "14px",
                  lineHeight: "20px",
                }}
              >
                {stat.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default NotreMission;
