"use client";

import React, { useState, useEffect } from "react";

const StatusCard = ({
  title,
  value,
  change,
  changeType = "positive", // positive, negative, neutral
  icon,
  description,
  trend = [], // Array of values for mini chart
  isLoading = false,
  className = "",
  ...props
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Animate value on mount
  useEffect(() => {
    setIsVisible(true);
    if (typeof value === "string" && value.includes("%")) {
      const numericValue = parseFloat(value.replace("%", ""));
      animateNumber(0, numericValue, 1000);
    } else if (typeof value === "string" && value.includes(",")) {
      const numericValue = parseFloat(value.replace(/,/g, ""));
      animateNumber(0, numericValue, 1500);
    }
  }, [value]);

  const animateNumber = (start, end, duration) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = start + (end - start) * easeOutQuart;
      setAnimatedValue(current);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  };

  const getChangeStyles = () => {
    switch (changeType) {
      case "positive":
        return {
          color: "rgb(34, 197, 94)", // success-500
          bg: "rgba(34, 197, 94, 0.1)",
          gradient: "var(--gradient-success)",
        };
      case "negative":
        return {
          color: "rgb(239, 68, 68)", // error-500
          bg: "rgba(239, 68, 68, 0.1)",
          gradient: "var(--gradient-error)",
        };
      default:
        return {
          color: "rgb(115, 115, 115)", // neutral-500
          bg: "rgba(115, 115, 115, 0.1)",
          gradient: "var(--gradient-info)",
        };
    }
  };

  const getChangeIcon = () => {
    if (changeType === "positive") {
      return (
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 14L12 9L17 14"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
    } else if (changeType === "negative") {
      return (
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 10L12 15L17 10"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
    }
    return (
      <svg
        width="14"
        height="14"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 12H19"
          stroke="currentColor"
          strokeWidth="2.5"
          strokeLinecap="round"
        />
      </svg>
    );
  };

  const formatDisplayValue = () => {
    if (typeof value === "string" && value.includes("%")) {
      return `${animatedValue.toFixed(1)}%`;
    } else if (typeof value === "string" && value.includes(",")) {
      return animatedValue.toLocaleString();
    }
    return value;
  };

  const changeStyles = getChangeStyles();

  if (isLoading) {
    return (
      <div
        className={`
        relative overflow-hidden
        bg-white/80 backdrop-blur-sm
        border border-neutral-200/60
        rounded-2xl p-6
        ${className}
      `}
      >
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-neutral-200 rounded-lg w-20"></div>
            <div className="w-10 h-10 bg-neutral-200 rounded-xl"></div>
          </div>
          <div className="h-8 bg-neutral-200 rounded-lg w-24 mb-3"></div>
          <div className="h-4 bg-neutral-200 rounded-lg w-16"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`
        group relative overflow-hidden
        bg-white/80 backdrop-blur-sm
        border border-neutral-200/60
        rounded-2xl p-6
        transition-all duration-300 ease-out
        hover:bg-white/90 hover:border-neutral-300/80
        hover:shadow-lg hover:shadow-neutral-900/5
        hover:-translate-y-1
        cursor-pointer
        ${isVisible ? "animate-fade-in" : "opacity-0"}
        ${className}
      `}
      style={{
        boxShadow:
          "0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)",
      }}
      {...props}
    >
      {/* Gradient overlay on hover */}
      <div
        className="absolute inset-0 opacity-0 group-hover:opacity-5 transition-opacity duration-300"
        style={{ background: changeStyles.gradient }}
      />

      {/* Header */}
      <div className="flex items-center justify-between mb-4 relative z-10">
        <h3 className="text-neutral-600 text-sm font-medium tracking-wide uppercase">
          {title}
        </h3>
        {icon && (
          <div
            className="w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110"
            style={{
              background: `linear-gradient(135deg, ${changeStyles.bg}, ${changeStyles.bg}80)`,
              color: changeStyles.color,
            }}
          >
            {icon}
          </div>
        )}
      </div>

      {/* Value with animation */}
      <div className="mb-3 relative z-10">
        <span className="text-neutral-900 text-3xl font-bold tracking-tight">
          {formatDisplayValue()}
        </span>
      </div>

      {/* Mini trend chart */}
      {trend.length > 0 && (
        <div className="mb-3 h-8 flex items-end gap-1 relative z-10">
          {trend.map((point, index) => (
            <div
              key={index}
              className="flex-1 rounded-sm transition-all duration-300 group-hover:opacity-80"
              style={{
                height: `${(point / Math.max(...trend)) * 100}%`,
                background: changeStyles.gradient,
                minHeight: "2px",
              }}
            />
          ))}
        </div>
      )}

      {/* Change and Description */}
      <div className="flex items-center justify-between relative z-10">
        {change && (
          <div
            className="flex items-center gap-1.5 px-2 py-1 rounded-lg text-sm font-medium transition-all duration-300"
            style={{
              color: changeStyles.color,
              backgroundColor: changeStyles.bg,
            }}
          >
            {getChangeIcon()}
            <span>{change}</span>
          </div>
        )}
        {description && (
          <span className="text-neutral-500 text-sm font-medium">
            {description}
          </span>
        )}
      </div>

      {/* Subtle border glow effect */}
      <div className="absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
        <div
          className="absolute inset-0 rounded-2xl"
          style={{
            background: `linear-gradient(135deg, ${changeStyles.color}20, transparent 50%, ${changeStyles.color}10)`,
            filter: "blur(1px)",
          }}
        />
      </div>
    </div>
  );
};

export default StatusCard;
