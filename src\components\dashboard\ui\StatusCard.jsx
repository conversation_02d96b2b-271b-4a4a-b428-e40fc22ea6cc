"use client";

import React, { useState, useEffect } from "react";

const StatusCard = ({
  title,
  value,
  change,
  changeType = "positive", // positive, negative, neutral
  icon,
  description,
  trend = [], // Array of values for mini chart
  isLoading = false,
  className = "",
  ...props
}) => {
  const [animatedValue, setAnimatedValue] = useState(0);
  const [isVisible, setIsVisible] = useState(false);

  // Animate value on mount
  useEffect(() => {
    setIsVisible(true);
    if (typeof value === "string" && value.includes("%")) {
      const numericValue = parseFloat(value.replace("%", ""));
      animateNumber(0, numericValue, 1000);
    } else if (typeof value === "string" && value.includes(",")) {
      const numericValue = parseFloat(value.replace(/,/g, ""));
      animateNumber(0, numericValue, 1500);
    }
  }, [value]);

  const animateNumber = (start, end, duration) => {
    const startTime = Date.now();
    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);
      const current = start + (end - start) * easeOutQuart;
      setAnimatedValue(current);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    requestAnimationFrame(animate);
  };

  const getChangeStyles = () => {
    switch (changeType) {
      case "positive":
        return {
          color: "text-green-600",
          bg: "bg-green-50",
          border: "border-green-200",
        };
      case "negative":
        return {
          color: "text-red-600",
          bg: "bg-red-50",
          border: "border-red-200",
        };
      default:
        return {
          color: "text-gray-600",
          bg: "bg-gray-50",
          border: "border-gray-200",
        };
    }
  };

  const getChangeIcon = () => {
    if (changeType === "positive") {
      return (
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 14L12 9L17 14"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
    } else if (changeType === "negative") {
      return (
        <svg
          width="14"
          height="14"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M7 10L12 15L17 10"
            stroke="currentColor"
            strokeWidth="2.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      );
    }
    return (
      <svg
        width="14"
        height="14"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M5 12H19"
          stroke="currentColor"
          strokeWidth="2.5"
          strokeLinecap="round"
        />
      </svg>
    );
  };

  const formatDisplayValue = () => {
    if (typeof value === "string" && value.includes("%")) {
      return `${animatedValue.toFixed(1)}%`;
    } else if (typeof value === "string" && value.includes(",")) {
      return animatedValue.toLocaleString();
    }
    return value;
  };

  const changeStyles = getChangeStyles();

  if (isLoading) {
    return (
      <div
        className={`
        relative overflow-hidden
        bg-white/80 backdrop-blur-sm
        border border-gray-200/60
        rounded-2xl p-6
        ${className}
      `}
      >
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 rounded-lg w-20"></div>
            <div className="w-10 h-10 bg-gray-200 rounded-xl"></div>
          </div>
          <div className="h-8 bg-gray-200 rounded-lg w-24 mb-3"></div>
          <div className="h-4 bg-gray-200 rounded-lg w-16"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`
        group relative overflow-hidden
        bg-white border border-gray-200
        rounded-2xl p-6
        transition-all duration-300 ease-out
        hover:bg-gray-50 hover:border-gray-300
        hover:shadow-lg hover:-translate-y-1
        cursor-pointer
        ${isVisible ? "animate-fade-in" : "opacity-0"}
        ${className}
      `}
      style={{
        boxShadow:
          "0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)",
      }}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-gray-600 text-sm font-medium tracking-wide uppercase">
          {title}
        </h3>
        {icon && (
          <div
            className={`w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 group-hover:scale-110 ${changeStyles.bg} text-[#4B935E]`}
          >
            {icon}
          </div>
        )}
      </div>

      {/* Value with animation */}
      <div className="mb-3">
        <span className="text-gray-900 text-3xl font-bold tracking-tight">
          {formatDisplayValue()}
        </span>
      </div>

      {/* Mini trend chart */}
      {trend.length > 0 && (
        <div className="mb-3 h-8 flex items-end gap-1">
          {trend.map((point, index) => (
            <div
              key={index}
              className="flex-1 rounded-sm transition-all duration-300 group-hover:opacity-80 bg-[#4B935E]"
              style={{
                height: `${(point / Math.max(...trend)) * 100}%`,
                minHeight: "2px",
              }}
            />
          ))}
        </div>
      )}

      {/* Change and Description */}
      <div className="flex items-center justify-between">
        {change && (
          <div
            className={`flex items-center gap-1.5 px-2 py-1 rounded-lg text-sm font-medium transition-all duration-300 ${changeStyles.color} ${changeStyles.bg} ${changeStyles.border} border`}
          >
            {getChangeIcon()}
            <span>{change}</span>
          </div>
        )}
        {description && (
          <span className="text-gray-500 text-sm font-medium">
            {description}
          </span>
        )}
      </div>
    </div>
  );
};

export default StatusCard;
