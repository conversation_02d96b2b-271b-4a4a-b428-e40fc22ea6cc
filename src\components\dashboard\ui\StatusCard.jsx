"use client";

import React from "react";

const StatusCard = ({
  title,
  value,
  change,
  changeType = "positive", // positive, negative, neutral
  icon,
  description,
  className = "",
  ...props
}) => {
  const getChangeColor = () => {
    switch (changeType) {
      case "positive":
        return "text-[#4B935E]";
      case "negative":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  const getChangeIcon = () => {
    if (changeType === "positive") {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 14L12 9L17 14" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      );
    } else if (changeType === "negative") {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M7 10L12 15L17 10" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      );
    }
    return null;
  };

  return (
    <div
      className={`
        bg-white
        border border-[#E5E7EB]
        rounded-lg
        p-6
        shadow-sm hover:shadow-md transition-shadow duration-200
        ${className}
      `}
      style={{
        boxShadow: "0px 2px 4px -2px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.1)",
      }}
      {...props}
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <h3
          className="text-[#6B7280] text-sm font-medium"
          style={{
            fontFamily: "Poppins, sans-serif",
            fontWeight: 500,
          }}
        >
          {title}
        </h3>
        {icon && (
          <div className="w-8 h-8 bg-[#4B935E]/10 rounded-lg flex items-center justify-center text-[#4B935E]">
            {icon}
          </div>
        )}
      </div>

      {/* Value */}
      <div className="mb-2">
        <span
          className="text-[#1F2421] text-2xl font-bold"
          style={{
            fontFamily: "Poppins, sans-serif",
            fontWeight: 700,
          }}
        >
          {value}
        </span>
      </div>

      {/* Change and Description */}
      <div className="flex items-center justify-between">
        {change && (
          <div className={`flex items-center gap-1 ${getChangeColor()}`}>
            {getChangeIcon()}
            <span
              className="text-sm font-medium"
              style={{
                fontFamily: "Poppins, sans-serif",
                fontWeight: 500,
              }}
            >
              {change}
            </span>
          </div>
        )}
        {description && (
          <span
            className="text-[#6B7280] text-sm"
            style={{
              fontFamily: "Poppins, sans-serif",
              fontWeight: 400,
            }}
          >
            {description}
          </span>
        )}
      </div>
    </div>
  );
};

export default StatusCard;
