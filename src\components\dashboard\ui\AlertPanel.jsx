"use client";

import React, { useState, useEffect } from "react";

const AlertPanel = ({
  alerts = [],
  title = "System Alerts",
  className = "",
  onAlertClick,
  onDismiss,
  isLoading = false,
  ...props
}) => {
  const [visibleAlerts, setVisibleAlerts] = useState([]);
  const [animatingOut, setAnimatingOut] = useState(new Set());

  useEffect(() => {
    // Stagger alert animations
    alerts.forEach((alert, index) => {
      setTimeout(() => {
        setVisibleAlerts((prev) => [...prev, alert.id || index]);
      }, index * 100);
    });
  }, [alerts]);

  const getAlertIcon = (severity) => {
    switch (severity) {
      case "critical":
        return (
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2.5"
            />
            <line
              x1="12"
              y1="8"
              x2="12"
              y2="12"
              stroke="currentColor"
              strokeWidth="2.5"
            />
            <line
              x1="12"
              y1="16"
              x2="12.01"
              y2="16"
              stroke="currentColor"
              strokeWidth="2.5"
            />
          </svg>
        );
      case "warning":
        return (
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"
              stroke="currentColor"
              strokeWidth="2"
            />
            <line
              x1="12"
              y1="9"
              x2="12"
              y2="13"
              stroke="currentColor"
              strokeWidth="2"
            />
            <line
              x1="12"
              y1="17"
              x2="12.01"
              y2="17"
              stroke="currentColor"
              strokeWidth="2"
            />
          </svg>
        );
      case "info":
        return (
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
            <line
              x1="12"
              y1="16"
              x2="12"
              y2="12"
              stroke="currentColor"
              strokeWidth="2"
            />
            <line
              x1="12"
              y1="8"
              x2="12.01"
              y2="8"
              stroke="currentColor"
              strokeWidth="2"
            />
          </svg>
        );
      default:
        return (
          <svg
            width="18"
            height="18"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="2"
            />
            <path d="M9,12l2,2 4,-4" stroke="currentColor" strokeWidth="2" />
          </svg>
        );
    }
  };

  const getAlertStyles = (severity) => {
    switch (severity) {
      case "critical":
        return {
          bg: "rgba(239, 68, 68, 0.05)",
          border: "rgba(239, 68, 68, 0.2)",
          icon: "rgb(239, 68, 68)",
          text: "rgb(153, 27, 27)",
          gradient: "var(--gradient-error)",
          pulse: "animate-pulse-slow",
        };
      case "warning":
        return {
          bg: "rgba(245, 158, 11, 0.05)",
          border: "rgba(245, 158, 11, 0.2)",
          icon: "rgb(245, 158, 11)",
          text: "rgb(146, 64, 14)",
          gradient: "var(--gradient-warning)",
          pulse: "",
        };
      case "info":
        return {
          bg: "rgba(59, 130, 246, 0.05)",
          border: "rgba(59, 130, 246, 0.2)",
          icon: "rgb(59, 130, 246)",
          text: "rgb(30, 64, 175)",
          gradient: "var(--gradient-info)",
          pulse: "",
        };
      default:
        return {
          bg: "rgba(34, 197, 94, 0.05)",
          border: "rgba(34, 197, 94, 0.2)",
          icon: "rgb(34, 197, 94)",
          text: "rgb(21, 128, 61)",
          gradient: "var(--gradient-success)",
          pulse: "",
        };
    }
  };

  const handleDismiss = (alert) => {
    const alertId = alert.id || alerts.indexOf(alert);
    setAnimatingOut((prev) => new Set([...prev, alertId]));

    setTimeout(() => {
      onDismiss && onDismiss(alert);
      setAnimatingOut((prev) => {
        const newSet = new Set(prev);
        newSet.delete(alertId);
        return newSet;
      });
    }, 300);
  };

  if (isLoading) {
    return (
      <div
        className={`
        bg-white/80 backdrop-blur-sm
        border border-neutral-200/60
        rounded-2xl
        ${className}
      `}
      >
        <div className="px-6 py-4 border-b border-neutral-200/60">
          <div className="h-6 bg-neutral-200 rounded-lg w-32 skeleton"></div>
        </div>
        <div className="p-6 space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-start gap-3">
              <div className="w-9 h-9 bg-neutral-200 rounded-xl skeleton"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-neutral-200 rounded-lg w-3/4 skeleton"></div>
                <div className="h-3 bg-neutral-200 rounded-lg w-full skeleton"></div>
                <div className="h-3 bg-neutral-200 rounded-lg w-1/4 skeleton"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div
      className={`
        bg-white/80 backdrop-blur-sm
        border border-neutral-200/60
        rounded-2xl
        transition-all duration-300 ease-out
        hover:bg-white/90 hover:border-neutral-300/80
        hover:shadow-lg hover:shadow-neutral-900/5
        animate-fade-in
        ${className}
      `}
      style={{
        boxShadow:
          "0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -2px rgba(0, 0, 0, 0.03)",
      }}
      {...props}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-neutral-200/60">
        <div className="flex items-center justify-between">
          <h3 className="text-neutral-900 text-lg font-semibold tracking-tight">
            {title}
          </h3>
          {alerts.length > 0 && (
            <div className="flex items-center gap-2">
              <span className="text-xs font-medium text-neutral-500 bg-neutral-100 px-2 py-1 rounded-full">
                {alerts.length}
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Alerts List */}
      <div className="p-6">
        {alerts.length === 0 ? (
          <div className="text-center py-12">
            <div className="w-16 h-16 bg-gradient-to-br from-green-100 to-green-50 rounded-2xl flex items-center justify-center mx-auto mb-4">
              <svg
                width="28"
                height="28"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="rgb(34, 197, 94)"
                  strokeWidth="2"
                />
                <path
                  d="M9,12l2,2 4,-4"
                  stroke="rgb(34, 197, 94)"
                  strokeWidth="2"
                />
              </svg>
            </div>
            <h4 className="text-neutral-900 font-medium mb-1">All Clear</h4>
            <p className="text-neutral-500 text-sm">
              No active alerts at this time
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {alerts.map((alert, index) => {
              const alertId = alert.id || index;
              const styles = getAlertStyles(alert.severity);
              const isVisible = visibleAlerts.includes(alertId);
              const isAnimatingOut = animatingOut.has(alertId);
              return (
                <div
                  key={alertId}
                  className={`
                    group relative overflow-hidden
                    border rounded-xl p-4
                    cursor-pointer transition-all duration-300 ease-out
                    hover:shadow-md hover:shadow-neutral-900/5
                    hover:-translate-y-0.5
                    ${
                      isVisible ? "animate-slide-in" : "opacity-0 translate-x-4"
                    }
                    ${isAnimatingOut ? "animate-scale-out opacity-0" : ""}
                    ${styles.pulse}
                  `}
                  style={{
                    backgroundColor: styles.bg,
                    borderColor: styles.border,
                  }}
                  onClick={() => onAlertClick && onAlertClick(alert)}
                >
                  {/* Gradient overlay */}
                  <div
                    className="absolute inset-0 opacity-0 group-hover:opacity-10 transition-opacity duration-300"
                    style={{ background: styles.gradient }}
                  />

                  <div className="flex items-start gap-3 relative z-10">
                    <div
                      className="w-9 h-9 rounded-xl flex items-center justify-center flex-shrink-0 transition-transform duration-300 group-hover:scale-110"
                      style={{
                        backgroundColor: `${styles.icon}15`,
                        color: styles.icon,
                      }}
                    >
                      {getAlertIcon(alert.severity)}
                    </div>

                    <div className="flex-1 min-w-0">
                      <h4
                        className="font-semibold text-sm mb-1 tracking-tight"
                        style={{ color: styles.text }}
                      >
                        {alert.title}
                      </h4>
                      <p
                        className="text-sm leading-relaxed mb-2"
                        style={{ color: `${styles.text}CC` }}
                      >
                        {alert.message}
                      </p>
                      {alert.timestamp && (
                        <div className="flex items-center gap-1">
                          <svg
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="12"
                              cy="12"
                              r="10"
                              stroke="currentColor"
                              strokeWidth="2"
                            />
                            <polyline
                              points="12,6 12,12 16,14"
                              stroke="currentColor"
                              strokeWidth="2"
                            />
                          </svg>
                          <span
                            className="text-xs font-medium"
                            style={{ color: `${styles.text}99` }}
                          >
                            {alert.timestamp}
                          </span>
                        </div>
                      )}
                    </div>

                    {onDismiss && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDismiss(alert);
                        }}
                        className="w-6 h-6 rounded-lg flex items-center justify-center transition-all duration-200 hover:bg-neutral-200/50 opacity-0 group-hover:opacity-100"
                        style={{ color: styles.icon }}
                      >
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <line
                            x1="18"
                            y1="6"
                            x2="6"
                            y2="18"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                          <line
                            x1="6"
                            y1="6"
                            x2="18"
                            y2="18"
                            stroke="currentColor"
                            strokeWidth="2"
                          />
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertPanel;
