"use client";

import React from "react";

const AlertPanel = ({
  alerts = [],
  title = "System Alerts",
  className = "",
  onAlertClick,
  onDismiss,
  ...props
}) => {
  const getAlertIcon = (severity) => {
    switch (severity) {
      case "critical":
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="8" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="16" x2="12.01" y2="16" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      case "warning":
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="9" x2="12" y2="13" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="17" x2="12.01" y2="17" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      case "info":
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="16" x2="12" y2="12" stroke="currentColor" strokeWidth="2"/>
            <line x1="12" y1="8" x2="12.01" y2="8" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
      default:
        return (
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
            <path d="M9,12l2,2 4,-4" stroke="currentColor" strokeWidth="2"/>
          </svg>
        );
    }
  };

  const getAlertColors = (severity) => {
    switch (severity) {
      case "critical":
        return {
          bg: "bg-red-50",
          border: "border-red-200",
          icon: "text-red-500",
          text: "text-red-800",
        };
      case "warning":
        return {
          bg: "bg-yellow-50",
          border: "border-yellow-200",
          icon: "text-yellow-500",
          text: "text-yellow-800",
        };
      case "info":
        return {
          bg: "bg-blue-50",
          border: "border-blue-200",
          icon: "text-blue-500",
          text: "text-blue-800",
        };
      default:
        return {
          bg: "bg-green-50",
          border: "border-green-200",
          icon: "text-green-500",
          text: "text-green-800",
        };
    }
  };

  return (
    <div
      className={`
        bg-white
        border border-[#E5E7EB]
        rounded-lg
        shadow-sm
        ${className}
      `}
      style={{
        boxShadow: "0px 2px 4px -2px rgba(0,0,0,0.05), 0px 4px 6px -1px rgba(0,0,0,0.1)",
      }}
      {...props}
    >
      {/* Header */}
      <div className="px-6 py-4 border-b border-[#E5E7EB]">
        <h3
          className="text-[#1F2421] text-lg font-semibold"
          style={{
            fontFamily: "Poppins, sans-serif",
            fontWeight: 600,
          }}
        >
          {title}
        </h3>
      </div>

      {/* Alerts List */}
      <div className="p-6">
        {alerts.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 bg-[#4B935E]/10 rounded-full flex items-center justify-center mx-auto mb-3 text-[#4B935E]">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <circle cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
                <path d="M9,12l2,2 4,-4" stroke="currentColor" strokeWidth="2"/>
              </svg>
            </div>
            <p
              className="text-[#6B7280] text-sm"
              style={{
                fontFamily: "Poppins, sans-serif",
                fontWeight: 400,
              }}
            >
              No active alerts
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {alerts.map((alert, index) => {
              const colors = getAlertColors(alert.severity);
              return (
                <div
                  key={alert.id || index}
                  className={`
                    ${colors.bg} ${colors.border}
                    border rounded-lg p-4
                    cursor-pointer hover:shadow-sm transition-shadow duration-200
                  `}
                  onClick={() => onAlertClick && onAlertClick(alert)}
                >
                  <div className="flex items-start gap-3">
                    <div className={`${colors.icon} flex-shrink-0 mt-0.5`}>
                      {getAlertIcon(alert.severity)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4
                        className={`${colors.text} font-medium text-sm mb-1`}
                        style={{
                          fontFamily: "Poppins, sans-serif",
                          fontWeight: 500,
                        }}
                      >
                        {alert.title}
                      </h4>
                      <p
                        className={`${colors.text} text-sm opacity-80`}
                        style={{
                          fontFamily: "Poppins, sans-serif",
                          fontWeight: 400,
                        }}
                      >
                        {alert.message}
                      </p>
                      {alert.timestamp && (
                        <p
                          className={`${colors.text} text-xs opacity-60 mt-1`}
                          style={{
                            fontFamily: "Poppins, sans-serif",
                            fontWeight: 400,
                          }}
                        >
                          {alert.timestamp}
                        </p>
                      )}
                    </div>
                    {onDismiss && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onDismiss(alert);
                        }}
                        className={`${colors.icon} hover:opacity-70 transition-opacity duration-200`}
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <line x1="18" y1="6" x2="6" y2="18" stroke="currentColor" strokeWidth="2"/>
                          <line x1="6" y1="6" x2="18" y2="18" stroke="currentColor" strokeWidth="2"/>
                        </svg>
                      </button>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default AlertPanel;
