"use client";

import React, { useState } from "react";
import Image from "next/image";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="w-full max-w-6xl mx-auto mt-4 sm:mt-6 lg:mt-[25px] mb-4 px-4 sm:px-6 lg:px-8">
      <div
        className="flex items-center justify-between w-full p-3 sm:p-4 lg:px-8 lg:py-4 rounded-2xl bg-white border border-gray-200 shadow-sm"
        style={{
          gap: "clamp(20px, 4vw, 117px)",
        }}
      >
        {/* Logo */}
        <div className="flex items-center">
          <Image
            src="/logo.svg"
            alt="Logo"
            width={40}
            height={40}
            className="w-8 h-8 sm:w-10 sm:h-10"
          />
        </div>

        {/* Desktop Navigation Links */}
        <div className="hidden lg:flex items-center space-x-8">
          <a
            href="#"
            className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium text-sm xl:text-base"
          >
            Accueil
          </a>
          <a
            href="#"
            className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium text-sm xl:text-base"
          >
            À propos
          </a>
          <a
            href="#"
            className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium text-sm xl:text-base"
          >
            Projets
          </a>
          <a
            href="#"
            className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium text-sm xl:text-base"
          >
            Activités
          </a>
          <a
            href="#"
            className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium text-sm xl:text-base"
          >
            Contact
          </a>
        </div>

        {/* Desktop Action Buttons */}
        <div className="hidden lg:flex items-center space-x-3">
          <a
            href="/signup"
            className="bg-[#4B935E] text-white px-4 py-2 lg:px-6 lg:py-2 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 text-sm xl:text-base"
          >
            Sign up
          </a>
          <a
            href="/login"
            className="border border-[#E5E7EB] text-gray-700 px-4 py-2 lg:px-6 lg:py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 text-sm xl:text-base"
          >
            Log in
          </a>
        </div>

        {/* Mobile Menu Button */}
        <button
          className="lg:hidden p-2"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
          aria-label="Toggle menu"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-gray-700"
          >
            <path
              d="M3 12H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 6H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M3 18H21"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden mt-4 p-4 bg-white rounded-2xl border border-gray-200 shadow-lg">
          <div className="flex flex-col space-y-4">
            <a
              href="#"
              className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium py-2"
            >
              Accueil
            </a>
            <a
              href="#"
              className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium py-2"
            >
              À propos
            </a>
            <a
              href="#"
              className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium py-2"
            >
              Projets
            </a>
            <a
              href="#"
              className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium py-2"
            >
              Activités
            </a>
            <a
              href="#"
              className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium py-2"
            >
              Contact
            </a>
            <div className="flex flex-col space-y-3 pt-4 border-t border-gray-200">
              <a
                href="/signup"
                className="bg-[#4B935E] text-white px-6 py-3 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200 w-full text-center"
              >
                Sign up
              </a>
              <a
                href="/login"
                className="border border-[#E5E7EB] text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200 w-full text-center"
              >
                Log in
              </a>
            </div>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
