"use client";

import React, { useState } from "react";
import Image from "next/image";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav
      className="w-full max-w-[1287px] mx-auto mt-4 sm:mt-6 lg:mt-[25px] mb-4 px-4 sm:px-6 lg:px-0"
      style={{
        display: "inline-flex",
        padding: "clamp(12px, 1.2vw, 17px) clamp(20px, 3.5vw, 50px)",
        alignItems: "center",
        gap: "clamp(20px, 8vw, 117px)",
        borderRadius: "clamp(12px, 1.2vw, 16px)",
        border: "1px solid #E5E7EB",
        background: "#FFF",
        opacity: 1,
      }}
    >
      {/* Logo */}
      <div className="flex items-center">
        <Image
          src="/logo.svg"
          alt="Logo"
          width={40}
          height={40}
          className="w-10 h-10"
        />
      </div>

      {/* Mobile Menu Button */}
      <button
        className="lg:hidden p-2"
        onClick={() => setIsMenuOpen(!isMenuOpen)}
        aria-label="Toggle menu"
      >
        <svg
          width="24"
          height="24"
          viewBox="0 0 24 24"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          className="text-gray-700"
        >
          <path
            d="M3 12H21"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M3 6H21"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M3 18H21"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </button>

      {/* Navigation Links */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-4 lg:space-y-0 lg:space-x-8`}
      >
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Accueil
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          À propos
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Projets
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Activités
        </a>
        <a
          href="#"
          className="text-gray-700 hover:text-[#4B935E] transition-colors duration-200 font-medium"
        >
          Contact
        </a>
      </div>

      {/* Action Buttons */}
      <div
        className={`${
          isMenuOpen ? "flex" : "hidden"
        } lg:flex flex-col lg:flex-row items-center space-y-3 lg:space-y-0 lg:space-x-3`}
      >
        <button className="bg-[#4B935E] text-white px-6 py-2 rounded-lg font-medium hover:bg-[#3d7a4e] transition-colors duration-200">
          Sign up
        </button>
        <button className="border border-[#E5E7EB] text-gray-700 px-6 py-2 rounded-lg font-medium hover:bg-gray-50 transition-colors duration-200">
          Log in
        </button>
      </div>
    </nav>
  );
};

export default Navbar;
